// Netflix-style Movie Recommender JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Movie card hover effects
    const movieCards = document.querySelectorAll('.movie-card');
    
    movieCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.zIndex = '10';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.zIndex = '1';
        });
    });

    // Search functionality
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // Here you would typically make an AJAX call to search for movies
            console.log('Searching for:', query);
            // For now, we'll just show an alert
            alert(`Searching for movies: "${query}"`);
        }
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // Form validation and enhancement
    const recommendationForm = document.querySelector('.recommendation-form');
    const userInput = document.querySelector('.user-input');
    
    if (recommendationForm) {
        recommendationForm.addEventListener('submit', function(e) {
            const userId = userInput.value.trim();
            
            if (!userId) {
                e.preventDefault();
                alert('Please enter a valid User ID');
                userInput.focus();
                return;
            }
            
            if (isNaN(userId) || parseInt(userId) <= 0) {
                e.preventDefault();
                alert('Please enter a valid positive number for User ID');
                userInput.focus();
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('.cta-button');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Recommendations...';
            submitBtn.disabled = true;
            
            // Re-enable button after a delay (in case of errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });
    }

    // Add loading animation to movie cards
    const movieImages = document.querySelectorAll('.movie-poster img');
    
    movieImages.forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });
        
        img.addEventListener('error', function() {
            this.src = 'https://via.placeholder.com/200x300/333333/ffffff?text=No+Image';
        });
    });

    // Carousel scroll functionality
    const movieCarousel = document.querySelector('.movie-carousel');
    
    if (movieCarousel) {
        let isScrolling = false;
        
        movieCarousel.addEventListener('wheel', function(e) {
            if (!isScrolling) {
                isScrolling = true;
                e.preventDefault();
                
                this.scrollLeft += e.deltaY > 0 ? 200 : -200;
                
                setTimeout(() => {
                    isScrolling = false;
                }, 100);
            }
        });
    }

    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe sections for scroll animations
    const sections = document.querySelectorAll('.featured-section, .search-section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(50px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });

    // Add click handlers for movie cards
    movieCards.forEach(card => {
        card.addEventListener('click', function() {
            const movieTitle = this.querySelector('.movie-info h3')?.textContent;
            if (movieTitle) {
                // Here you would typically open a modal with movie details
                console.log('Clicked on movie:', movieTitle);
                // For now, we'll show an alert
                alert(`You clicked on: ${movieTitle}\n\nMovie details would be shown in a modal here.`);
            }
        });
    });

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        // ESC key to close any modals (future implementation)
        if (e.key === 'Escape') {
            console.log('ESC pressed - would close modals');
        }
        
        // Arrow keys for carousel navigation
        if (movieCarousel) {
            if (e.key === 'ArrowLeft') {
                movieCarousel.scrollLeft -= 200;
            } else if (e.key === 'ArrowRight') {
                movieCarousel.scrollLeft += 200;
            }
        }
    });

    // Add touch/swipe support for mobile
    let startX = 0;
    let scrollLeft = 0;

    if (movieCarousel) {
        movieCarousel.addEventListener('touchstart', function(e) {
            startX = e.touches[0].pageX - this.offsetLeft;
            scrollLeft = this.scrollLeft;
        });

        movieCarousel.addEventListener('touchmove', function(e) {
            e.preventDefault();
            const x = e.touches[0].pageX - this.offsetLeft;
            const walk = (x - startX) * 2;
            this.scrollLeft = scrollLeft - walk;
        });
    }

    // Initialize tooltips (if needed)
    const playButtons = document.querySelectorAll('.play-btn');
    playButtons.forEach(btn => {
        btn.setAttribute('title', 'Play Movie');
    });

    console.log('MovieFlix initialized successfully!');
});

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#e50914' : '#28a745'};
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Export functions for use in other scripts
window.MovieFlix = {
    showNotification
};
