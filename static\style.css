/* Netflix-style Movie Recommender CSS */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Netflix Sans', 'Helvetica Neue', Arial, sans-serif;
    background-color: #141414;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.7) 10%, transparent);
    z-index: 1000;
    transition: background-color 0.3s ease;
    padding: 1rem 0;
}

.navbar.scrolled {
    background-color: #141414;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-logo h1 {
    color: #e50914;
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: -1px;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #e50914;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #e50914;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.7)), 
                url('https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    padding: 0 2rem;
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 3rem;
    color: #cccccc;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.hero-form {
    margin-top: 2rem;
}

.input-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.user-input {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    border: none;
    border-radius: 8px;
    background-color: rgba(255,255,255,0.1);
    color: #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    min-width: 250px;
    transition: all 0.3s ease;
}

.user-input:focus {
    outline: none;
    background-color: rgba(255,255,255,0.2);
    border-color: #e50914;
    box-shadow: 0 0 20px rgba(229,9,20,0.3);
}

.user-input::placeholder {
    color: #cccccc;
}

.cta-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    background: linear-gradient(45deg, #e50914, #f40612);
    color: #ffffff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(229,9,20,0.3);
}

.cta-button:hover {
    background: linear-gradient(45deg, #f40612, #e50914);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(229,9,20,0.4);
}

/* Featured Section */
.featured-section {
    padding: 4rem 0;
    background-color: #141414;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #ffffff;
}

/* Movie Carousel */
.movie-carousel {
    overflow-x: auto;
    padding: 1rem 0;
}

.movie-row {
    display: flex;
    gap: 1rem;
    min-width: max-content;
    padding-bottom: 1rem;
}

.movie-card {
    position: relative;
    min-width: 200px;
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.movie-card:hover {
    transform: scale(1.05);
    z-index: 10;
}

.movie-poster {
    position: relative;
    width: 100%;
    height: 100%;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 1rem;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-btn {
    background-color: rgba(255,255,255,0.2);
    border: 2px solid #ffffff;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.play-btn:hover {
    background-color: #e50914;
    border-color: #e50914;
    transform: scale(1.1);
}

.movie-info {
    text-align: center;
}

.movie-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.movie-info p {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    color: #ffd700;
}

/* Search Section */
.search-section {
    padding: 4rem 0;
    background-color: #1a1a1a;
}

.search-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.search-box {
    display: flex;
    background-color: rgba(255,255,255,0.1);
    border-radius: 50px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.search-input {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border: none;
    background: transparent;
    color: #ffffff;
    min-width: 400px;
    outline: none;
}

.search-input::placeholder {
    color: #cccccc;
}

.search-btn {
    padding: 1rem 2rem;
    background: linear-gradient(45deg, #e50914, #f40612);
    border: none;
    color: #ffffff;
    cursor: pointer;
    font-size: 1.1rem;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: linear-gradient(45deg, #f40612, #e50914);
}

/* Footer */
.footer {
    background-color: #000000;
    padding: 2rem 0;
    text-align: center;
    color: #666666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .user-input {
        min-width: auto;
        width: 100%;
    }
    
    .nav-menu {
        display: none;
    }
    
    .movie-card {
        min-width: 150px;
        height: 225px;
    }
    
    .search-input {
        min-width: 250px;
    }
}

/* Results Page Styles */
.results-header {
    background: linear-gradient(135deg, #141414 0%, #1a1a1a 100%);
    padding: 8rem 0 4rem 0;
    border-bottom: 1px solid #333333;
}

.results-title-section {
    text-align: center;
    margin-bottom: 2rem;
}

.results-title {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.results-title i {
    color: #e50914;
}

.results-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 0;
}

.error-message {
    background-color: rgba(229, 9, 20, 0.1);
    border: 1px solid #e50914;
    color: #ff6b6b;
    padding: 1rem 2rem;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.back-button-container {
    text-align: center;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(45deg, #e50914, #f40612);
    color: #ffffff;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(229,9,20,0.3);
}

.back-button:hover {
    background: linear-gradient(45deg, #f40612, #e50914);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(229,9,20,0.4);
    color: #ffffff;
}

/* Recommendations Grid */
.recommendations-section {
    padding: 4rem 0;
    background-color: #141414;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.recommendation-card {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #333333;
}

.recommendation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.recommendation-card .movie-poster {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.recommendation-card .movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.recommendation-card:hover .movie-poster img {
    transform: scale(1.05);
}

.recommendation-card .movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent 50%, rgba(0,0,0,0.9));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.recommendation-card:hover .movie-overlay {
    opacity: 1;
}

.movie-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.action-btn {
    background-color: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background-color: #e50914;
    border-color: #e50914;
    transform: scale(1.1);
}

.movie-details {
    padding: 1.5rem;
}

.movie-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.movie-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.recommendation-rank {
    background: linear-gradient(45deg, #e50914, #f40612);
    color: #ffffff;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.movie-description {
    color: #cccccc;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.movie-genres {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.genre-tag {
    background-color: rgba(255,255,255,0.1);
    color: #ffffff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(255,255,255,0.2);
}

/* Additional Section */
.additional-section {
    padding: 4rem 0;
    background-color: #1a1a1a;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #1a1a1a;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid #333333;
}

.close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    color: #ffffff;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    background-color: rgba(0,0,0,0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.close:hover {
    background-color: #e50914;
}

.modal-header {
    padding: 2rem;
    border-bottom: 1px solid #333333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #ffffff;
    font-size: 2rem;
    margin: 0;
}

.modal-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ffd700;
    font-size: 1.2rem;
}

.modal-body {
    display: flex;
    padding: 2rem;
    gap: 2rem;
}

.modal-poster {
    flex-shrink: 0;
}

.modal-poster img {
    width: 200px;
    height: 300px;
    object-fit: cover;
    border-radius: 8px;
}

.modal-info {
    flex: 1;
}

.modal-info p {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.modal-genres {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
}

.modal-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-btn.primary {
    background: linear-gradient(45deg, #e50914, #f40612);
    color: #ffffff;
}

.modal-btn.primary:hover {
    background: linear-gradient(45deg, #f40612, #e50914);
    transform: translateY(-2px);
}

.modal-btn.secondary {
    background-color: rgba(255,255,255,0.1);
    color: #ffffff;
    border: 1px solid rgba(255,255,255,0.3);
}

.modal-btn.secondary:hover {
    background-color: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 1rem;
    }

    .results-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .recommendations-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .modal-body {
        flex-direction: column;
        padding: 1rem;
    }

    .modal-poster img {
        width: 150px;
        height: 225px;
    }

    .modal-actions {
        flex-direction: column;
    }
}

/* Search Page Styles */
.search-header {
    background: linear-gradient(135deg, #141414 0%, #1a1a1a 100%);
    padding: 8rem 0 4rem 0;
    text-align: center;
    border-bottom: 1px solid #333333;
}

.search-title {
    font-size: 3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.search-title i {
    color: #e50914;
}

.search-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 0;
}

.search-main {
    padding: 4rem 0;
    background-color: #141414;
    min-height: 60vh;
}

.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 0;
    color: #cccccc;
    font-size: 1.2rem;
}

.loading-indicator i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #e50914;
}

.search-results {
    margin-top: 3rem;
}

.search-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.search-result-card {
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #333333;
    cursor: pointer;
}

.search-result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.result-poster {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.result-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.search-result-card:hover .result-poster img {
    transform: scale(1.05);
}

.result-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent 50%, rgba(0,0,0,0.9));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.search-result-card:hover .result-overlay {
    opacity: 1;
}

.result-info {
    padding: 1.5rem;
}

.result-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.result-genres {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.no-results {
    text-align: center;
    padding: 4rem 0;
    color: #666666;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #333333;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #cccccc;
}

.no-results p {
    font-size: 1rem;
    color: #666666;
}

/* Responsive adjustments for search page */
@media (max-width: 768px) {
    .search-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .search-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .result-poster {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .search-results-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .result-poster {
        height: 200px;
    }

    .result-info {
        padding: 1rem;
    }
}
