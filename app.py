# app.py

from flask import Flask, request, render_template, jsonify
import pandas as pd
import numpy as np
import pickle
import os
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import linear_kernel
from sklearn.decomposition import TruncatedSVD
from sklearn.preprocessing import StandardScaler
import random

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'your-secret-key-here'

# Load datasets
movies = pd.read_csv("movies.csv")  # movieId, title, genres
ratings = pd.read_csv("ratings.csv")  # userId, movieId, rating

# Merge for user-movie ratings
movie_data = pd.merge(ratings, movies, on="movieId")

# Collaborative Filtering - Prepare User-Movie Matrix
user_movie_matrix = movie_data.pivot(index='userId', columns='movieId', values='rating').fillna(0)
X = user_movie_matrix.values
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train SVD model
svd = TruncatedSVD(n_components=50, random_state=42)
X_svd = svd.fit_transform(X_scaled)

# Save models for reuse
pickle.dump(svd, open("svd_model.pkl", "wb"))
pickle.dump(user_movie_matrix, open("user_movie_matrix.pkl", "wb"))

# Content-Based Filtering - TF-IDF on genres
tfidf = TfidfVectorizer(stop_words='english')
movies['genres'] = movies['genres'].fillna('')
tfidf_matrix = tfidf.fit_transform(movies['genres'])
cosine_sim = linear_kernel(tfidf_matrix, tfidf_matrix)
indices = pd.Series(movies.index, index=movies['title']).drop_duplicates()

def get_content_recommendations(title, top_n=10):
    if title not in indices:
        return []
    idx = indices[title]
    sim_scores = list(enumerate(cosine_sim[idx]))
    sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)
    sim_scores = sim_scores[1:top_n+1]
    movie_indices = [i[0] for i in sim_scores]
    return movies['title'].iloc[movie_indices].tolist()

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/recommend', methods=['POST'])
def recommend():
    try:
        user_id = int(request.form['user_id'])
        if user_id not in user_movie_matrix.index:
            return render_template('result.html', movies=[], message="User ID not found. Please try a different User ID (1-943).")

        # Collaborative recommendations
        svd_model = pickle.load(open("svd_model.pkl", "rb"))
        user_ratings = user_movie_matrix.loc[user_id].values.reshape(1, -1)
        user_scaled = scaler.transform(user_ratings)
        user_svd = svd_model.transform(user_scaled)
        scores = np.dot(user_svd, svd_model.components_)
        top_movie_indices = scores[0].argsort()[::-1]

        recommended_movie_ids = []
        for idx in top_movie_indices:
            movie_id = user_movie_matrix.columns[idx]
            if user_movie_matrix.loc[user_id, movie_id] == 0:  # Unseen movies
                recommended_movie_ids.append(movie_id)
            if len(recommended_movie_ids) == 10:
                break

        recommended_titles = movies[movies['movieId'].isin(recommended_movie_ids)]['title'].values

        return render_template('result.html', movies=recommended_titles, message=None)

    except ValueError:
        return render_template('result.html', movies=[], message="Please enter a valid User ID number.")
    except Exception as e:
        return render_template('result.html', movies=[], message=f"An error occurred: {str(e)}")

@app.route('/search', methods=['GET', 'POST'])
def search():
    if request.method == 'POST':
        query = request.form.get('query', '').strip()
        if not query:
            return jsonify({'movies': [], 'message': 'Please enter a search term.'})

        # Search in movie titles
        search_results = movies[movies['title'].str.contains(query, case=False, na=False)]

        if search_results.empty:
            return jsonify({'movies': [], 'message': f'No movies found for "{query}".'})

        # Limit to top 20 results
        search_results = search_results.head(20)
        movie_list = []

        for _, movie in search_results.iterrows():
            movie_list.append({
                'id': int(movie['movieId']),
                'title': movie['title'],
                'genres': movie['genres']
            })

        return jsonify({'movies': movie_list, 'message': None})

    return render_template('search.html')

@app.route('/movie/<int:movie_id>')
def movie_details(movie_id):
    movie = movies[movies['movieId'] == movie_id]
    if movie.empty:
        return jsonify({'error': 'Movie not found'}), 404

    movie_data = movie.iloc[0]

    # Get content-based recommendations for this movie
    content_recs = get_content_recommendations(movie_data['title'], top_n=5)

    return jsonify({
        'id': int(movie_data['movieId']),
        'title': movie_data['title'],
        'genres': movie_data['genres'],
        'similar_movies': content_recs
    })

@app.route('/api/trending')
def trending_movies():
    # Get movies with highest average ratings (minimum 50 ratings)
    movie_ratings = ratings.groupby('movieId').agg({
        'rating': ['mean', 'count']
    }).round(2)

    movie_ratings.columns = ['avg_rating', 'rating_count']
    movie_ratings = movie_ratings[movie_ratings['rating_count'] >= 50]
    movie_ratings = movie_ratings.sort_values('avg_rating', ascending=False)

    # Get top 20 trending movies
    top_movies = movie_ratings.head(20)
    trending_list = []

    for movie_id, data in top_movies.iterrows():
        movie_info = movies[movies['movieId'] == movie_id].iloc[0]
        trending_list.append({
            'id': int(movie_id),
            'title': movie_info['title'],
            'genres': movie_info['genres'],
            'rating': float(data['avg_rating']),
            'rating_count': int(data['rating_count'])
        })

    return jsonify({'movies': trending_list})

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)
