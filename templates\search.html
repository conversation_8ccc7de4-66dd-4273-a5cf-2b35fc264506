<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Movies - MovieFlix</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Netflix+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar scrolled">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>MovieFlix</h1>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">Home</a>
                <a href="#search" class="nav-link">Search</a>
                <a href="#results" class="nav-link">Results</a>
            </div>
        </div>
    </nav>

    <!-- Search Header -->
    <section class="search-header">
        <div class="container">
            <h1 class="search-title">
                <i class="fas fa-search"></i>
                Search Movies
            </h1>
            <p class="search-subtitle">Find your favorite movies from our extensive collection</p>
        </div>
    </section>

    <!-- Search Section -->
    <section class="search-main" id="search">
        <div class="container">
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="Search for movies..." class="search-input">
                    <button id="searchBtn" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- Loading indicator -->
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Searching movies...</span>
            </div>
            
            <!-- Search Results -->
            <div id="searchResults" class="search-results" style="display: none;">
                <h2 class="results-title">Search Results</h2>
                <div id="resultsGrid" class="search-results-grid">
                    <!-- Results will be populated here -->
                </div>
            </div>
            
            <!-- No Results Message -->
            <div id="noResults" class="no-results" style="display: none;">
                <i class="fas fa-film"></i>
                <h3>No movies found</h3>
                <p>Try searching with different keywords</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 MovieFlix. Powered by AI-driven recommendations.</p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const searchResults = document.getElementById('searchResults');
            const resultsGrid = document.getElementById('resultsGrid');
            const noResults = document.getElementById('noResults');

            function performSearch() {
                const query = searchInput.value.trim();
                if (!query) {
                    alert('Please enter a search term');
                    return;
                }

                // Show loading
                loadingIndicator.style.display = 'flex';
                searchResults.style.display = 'none';
                noResults.style.display = 'none';

                // Make AJAX request
                fetch('/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `query=${encodeURIComponent(query)}`
                })
                .then(response => response.json())
                .then(data => {
                    loadingIndicator.style.display = 'none';
                    
                    if (data.movies && data.movies.length > 0) {
                        displayResults(data.movies);
                        searchResults.style.display = 'block';
                    } else {
                        noResults.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    loadingIndicator.style.display = 'none';
                    alert('An error occurred while searching. Please try again.');
                });
            }

            function displayResults(movies) {
                resultsGrid.innerHTML = '';
                
                movies.forEach(movie => {
                    const movieCard = document.createElement('div');
                    movieCard.className = 'search-result-card';
                    
                    const genres = movie.genres.split('|').slice(0, 3).join(' • ');
                    
                    movieCard.innerHTML = `
                        <div class="result-poster">
                            <img src="https://via.placeholder.com/200x300/333333/ffffff?text=${encodeURIComponent(movie.title)}" 
                                 alt="${movie.title}"
                                 onerror="this.src='https://via.placeholder.com/200x300/333333/ffffff?text=No+Image'">
                            <div class="result-overlay">
                                <button class="play-btn" onclick="showMovieDetails('${movie.title}')">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                        </div>
                        <div class="result-info">
                            <h3 class="result-title">${movie.title}</h3>
                            <p class="result-genres">${genres}</p>
                            <div class="result-actions">
                                <button class="action-btn" title="Add to Watchlist">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="action-btn" title="Like">
                                    <i class="fas fa-thumbs-up"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    
                    resultsGrid.appendChild(movieCard);
                });
            }

            // Event listeners
            searchBtn.addEventListener('click', performSearch);
            
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Focus on search input
            searchInput.focus();
        });

        function showMovieDetails(movieTitle) {
            alert(`Movie Details for: ${movieTitle}\n\nIn a real application, this would show detailed information about the movie.`);
        }
    </script>
</body>
</html>
