<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Recommendations - MovieFlix</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Netflix+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar scrolled">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>MovieFlix</h1>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">Home</a>
                <a href="#recommendations" class="nav-link">Recommendations</a>
                <a href="#search" class="nav-link">Search</a>
            </div>
        </div>
    </nav>

    <!-- Results Header -->
    <section class="results-header">
        <div class="container">
            <div class="results-title-section">
                <h1 class="results-title">
                    <i class="fas fa-magic"></i>
                    Your Personalized Recommendations
                </h1>
                {% if message %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ message }}
                    </div>
                {% else %}
                    <p class="results-subtitle">Based on your viewing history and preferences</p>
                {% endif %}
            </div>
            <div class="back-button-container">
                <a href="/" class="back-button">
                    <i class="fas fa-arrow-left"></i>
                    Get New Recommendations
                </a>
            </div>
        </div>
    </section>

    {% if movies and movies|length > 0 %}
    <!-- Recommendations Section -->
    <section class="recommendations-section" id="recommendations">
        <div class="container">
            <div class="recommendations-grid">
                {% for movie in movies %}
                <div class="recommendation-card">
                    <div class="movie-poster">
                        <img src="https://image.tmdb.org/t/p/w500/{{ loop.index % 10 + 1 }}.jpg" 
                             alt="{{ movie }}" 
                             onerror="this.src='https://via.placeholder.com/300x450/333333/ffffff?text={{ movie|urlencode }}'">
                        <div class="movie-overlay">
                            <button class="play-btn" onclick="showMovieDetails('{{ movie }}')">
                                <i class="fas fa-play"></i>
                            </button>
                            <div class="movie-actions">
                                <button class="action-btn" title="Add to Watchlist">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="action-btn" title="Like">
                                    <i class="fas fa-thumbs-up"></i>
                                </button>
                                <button class="action-btn" title="More Info">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="movie-details">
                        <h3 class="movie-title">{{ movie }}</h3>
                        <div class="movie-meta">
                            <span class="recommendation-rank">#{{ loop.index }}</span>
                            <div class="rating">
                                <i class="fas fa-star"></i>
                                <span>{{ (8.5 - loop.index * 0.1)|round(1) }}</span>
                            </div>
                        </div>
                        <p class="movie-description">
                            A highly recommended movie based on your preferences and viewing history.
                        </p>
                        <div class="movie-genres">
                            <span class="genre-tag">Drama</span>
                            <span class="genre-tag">Action</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Additional Recommendations -->
    <section class="additional-section">
        <div class="container">
            <h2 class="section-title">You Might Also Like</h2>
            <div class="movie-carousel">
                <div class="movie-row">
                    {% for i in range(5) %}
                    <div class="movie-card">
                        <div class="movie-poster">
                            <img src="https://image.tmdb.org/t/p/w500/sample{{ i + 1 }}.jpg" 
                                 alt="Additional Movie {{ i + 1 }}"
                                 onerror="this.src='https://via.placeholder.com/200x300/333333/ffffff?text=Movie+{{ i + 1 }}'">
                            <div class="movie-overlay">
                                <button class="play-btn"><i class="fas fa-play"></i></button>
                                <div class="movie-info">
                                    <h3>Additional Movie {{ i + 1 }}</h3>
                                    <p>Genre • Year</p>
                                    <div class="rating">
                                        <i class="fas fa-star"></i>
                                        <span>{{ (8.0 + i * 0.1)|round(1) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 MovieFlix. Powered by AI-driven recommendations.</p>
        </div>
    </footer>

    <!-- Movie Details Modal (Hidden by default) -->
    <div id="movieModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-header">
                <h2 id="modalTitle">Movie Title</h2>
                <div class="modal-rating">
                    <i class="fas fa-star"></i>
                    <span id="modalRating">8.5</span>
                </div>
            </div>
            <div class="modal-body">
                <div class="modal-poster">
                    <img id="modalImage" src="" alt="Movie Poster">
                </div>
                <div class="modal-info">
                    <p id="modalDescription">Movie description will appear here...</p>
                    <div class="modal-genres" id="modalGenres">
                        <!-- Genres will be populated here -->
                    </div>
                    <div class="modal-actions">
                        <button class="modal-btn primary">
                            <i class="fas fa-play"></i>
                            Watch Now
                        </button>
                        <button class="modal-btn secondary">
                            <i class="fas fa-plus"></i>
                            Add to Watchlist
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Movie details modal functionality
        function showMovieDetails(movieTitle) {
            const modal = document.getElementById('movieModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalImage = document.getElementById('modalImage');
            const modalDescription = document.getElementById('modalDescription');
            
            modalTitle.textContent = movieTitle;
            modalImage.src = `https://via.placeholder.com/300x450/333333/ffffff?text=${encodeURIComponent(movieTitle)}`;
            modalDescription.textContent = `This is a detailed description of "${movieTitle}". In a real application, this would contain actual movie information from a database or API.`;
            
            modal.style.display = 'block';
        }

        // Close modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('movieModal');
            const closeBtn = document.querySelector('.close');
            
            closeBtn.onclick = function() {
                modal.style.display = 'none';
            }
            
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            }
        });

        // Show success notification
        {% if movies and movies|length > 0 %}
        document.addEventListener('DOMContentLoaded', function() {
            window.MovieFlix.showNotification('{{ movies|length }} personalized recommendations found!', 'success');
        });
        {% endif %}
    </script>
</body>
</html>
