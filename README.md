# MovieFlix - Netflix-Style Movie Recommender

A beautiful, Netflix-inspired movie recommendation website powered by machine learning algorithms.

## Features

### 🎬 **Netflix-Style UI**
- Modern, responsive design inspired by Netflix
- Smooth animations and hover effects
- Mobile-friendly interface
- Dark theme with red accent colors

### 🤖 **AI-Powered Recommendations**
- **Collaborative Filtering**: Uses SVD (Singular Value Decomposition) to find similar users
- **Content-Based Filtering**: Recommends movies based on genre similarity using TF-IDF
- Personalized recommendations for each user

### 🔍 **Advanced Search**
- Real-time movie search functionality
- Search by movie title
- Interactive search results with movie cards

### 📱 **Responsive Design**
- Works perfectly on desktop, tablet, and mobile devices
- Touch-friendly interface for mobile users
- Optimized for all screen sizes

## Technology Stack

### Backend
- **Flask**: Python web framework
- **Pandas**: Data manipulation and analysis
- **Scikit-learn**: Machine learning algorithms
- **NumPy**: Numerical computing

### Frontend
- **HTML5**: Modern semantic markup
- **CSS3**: Advanced styling with animations
- **JavaScript**: Interactive functionality
- **Font Awesome**: Beautiful icons

### Machine Learning
- **TF-IDF Vectorization**: Content-based recommendations
- **Truncated SVD**: Collaborative filtering
- **Cosine Similarity**: Content similarity calculation

## Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd movie_recommender
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open in browser**
   Navigate to `http://localhost:5000`

## Usage

### Getting Recommendations
1. Enter your User ID (1-943) on the home page
2. Click "Get My Recommendations"
3. View your personalized movie recommendations

### Searching Movies
1. Click on "Search" in the navigation
2. Enter a movie title in the search box
3. Browse through the search results

### Exploring Trending Movies
- The home page displays trending movies with high ratings
- Hover over movie cards to see additional information

## API Endpoints

- `GET /` - Home page
- `POST /recommend` - Get personalized recommendations
- `GET /search` - Search page
- `POST /search` - Search for movies
- `GET /api/trending` - Get trending movies
- `GET /movie/<id>` - Get movie details

## Data

The application uses the MovieLens dataset:
- **movies.csv**: Movie information (ID, title, genres)
- **ratings.csv**: User ratings (user ID, movie ID, rating)

## Features in Detail

### Recommendation Algorithm
1. **Collaborative Filtering**: 
   - Creates user-movie matrix
   - Applies SVD for dimensionality reduction
   - Finds similar users and recommends unseen movies

2. **Content-Based Filtering**:
   - Uses TF-IDF on movie genres
   - Calculates cosine similarity between movies
   - Recommends similar movies based on content

### User Interface
- **Hero Section**: Eye-catching landing area with call-to-action
- **Movie Cards**: Interactive cards with hover effects
- **Search Interface**: Real-time search with loading indicators
- **Responsive Grid**: Adaptive layout for different screen sizes

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Performance

- Fast loading times with optimized assets
- Efficient recommendation algorithms
- Responsive design for smooth user experience

## Future Enhancements

- User authentication and profiles
- Movie trailers and detailed information
- Watchlist functionality
- Rating system
- Social features (sharing, reviews)
- Integration with external movie APIs (TMDB, OMDB)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is open source and available under the MIT License.

---

**Enjoy discovering your next favorite movie with MovieFlix!** 🍿🎬
